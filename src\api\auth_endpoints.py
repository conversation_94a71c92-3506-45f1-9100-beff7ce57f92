from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Response
from fastapi.security import OAuth2PasswordRequestForm
from typing import Dict, Any, List
from datetime import timedelta
from sqlmodel import Session, select
import io

from src.auth import (
    UserA<PERSON>, Token, authenticate_user, create_access_token,
    get_current_user, create_user, ACCESS_TOKEN_EXPIRE_MINUTES
)
from src.database import get_session
from src.models.models import (
    UserCreate as UserCreateModel,
    UserRead,
    UserRating,
    Game,
    GameList,
    GameListType,
    User as UserModel
)
from src.api.endpoints import recommender  # Используем существующий экземпляр
from src.user_ratings import add_user_rating as add_rating, delete_user_rating, get_user_ratings
from src.game_lists import add_game_to_list as add_game, remove_game_from_list, get_user_game_lists

router = APIRouter(tags=["Аутентификация и пользователи"])

@router.post("/register", response_model=Dict[str, Any])
async def register_user(user_data: UserCreateModel, session: Session = Depends(get_session)):
    """
    Регистрация нового пользователя

    - **username**: Имя пользователя (уникальное)
    - **email**: Email пользователя (уникальный)
    - **password**: Пароль пользователя
    """
    try:
        user = create_user(user_data, session)
        return {
            "success": True,
            "message": "Пользователь успешно зарегистрирован",
            "username": user.username,
            "email": user.email,
            "id": user.id
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при регистрации: {str(e)}"
        )

@router.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    session: Session = Depends(get_session)
):
    """
    Получение токена доступа (вход в систему)

    - **username**: Имя пользователя
    - **password**: Пароль пользователя
    """
    user = authenticate_user(form_data.username, form_data.password, session)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Неверное имя пользователя или пароль",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/users/me", response_model=Dict[str, Any])
async def read_users_me(current_user: UserAuth = Depends(get_current_user)):
    """
    Получение информации о текущем пользователе

    Требует авторизации через токен
    """
    return {
        "username": current_user.username,
        "email": current_user.email,
        "id": current_user.id,
        "is_active": current_user.is_active,
        "has_avatar": current_user.has_avatar,
        "avatar_content_type": current_user.avatar_content_type
    }

@router.get("/users/me/recommendations", response_model=Dict[str, Any])
async def get_recommendations_for_current_user(
    top_n: int = 10,
    current_user: UserAuth = Depends(get_current_user)
):
    """
    Получение персональных рекомендаций игр для текущего пользователя

    - **top_n**: Количество рекомендаций (по умолчанию 10)

    Требует авторизации через токен
    """
    # Используем ID пользователя из объекта пользователя
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Получаем рекомендации для пользователя
    recommendations = recommender.recommend_for_user(user_id, top_n)

    return recommendations



@router.post("/users/me/ratings", response_model=Dict[str, Any])
async def add_rating_for_current_user(
    app_id: int,
    rating: int,
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Добавление или обновление оценки игры для текущего пользователя

    - **app_id**: ID игры
    - **rating**: Оценка от 1 до 5

    Требует авторизации через токен
    """
    # Используем ID пользователя из объекта пользователя
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Добавляем оценку через модуль user_ratings
    result = add_rating(user_id, app_id, rating, session)

    # Проверяем результат
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )

    # Также обновляем оценку в рекомендательной системе
    recommender.add_user_rating(user_id, app_id, rating)

    return result

@router.delete("/users/me/ratings/{app_id}", response_model=Dict[str, Any])
async def delete_rating_for_current_user(
    app_id: int,
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Удаление оценки игры для текущего пользователя

    - **app_id**: ID игры

    Требует авторизации через токен
    """
    # Используем ID пользователя из объекта пользователя
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Удаляем оценку через модуль user_ratings
    result = delete_user_rating(user_id, app_id, session)

    # Проверяем результат
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["error"]
        )

    # Также удаляем оценку из рекомендательной системы
    recommender.delete_user_rating(user_id, app_id)

    return result

@router.get("/users/me/ratings", response_model=Dict[str, Any])
async def get_ratings_for_current_user(
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Получение всех оценок текущего пользователя

    Требует авторизации через токен
    """
    # Используем ID пользователя из объекта пользователя
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Получаем оценки пользователя через модуль user_ratings
    ratings = get_user_ratings(user_id, session)

    return {
        "user_id": user_id,
        "username": current_user.username,
        "ratings": ratings
    }

@router.post("/users/me/game-lists/{list_type}/{app_id}", response_model=Dict[str, Any])
async def add_game_to_user_list(
    list_type: GameListType,
    app_id: int,
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Добавление игры в список пользователя

    - **list_type**: Тип списка (playing, planned, completed)
    - **app_id**: ID игры

    Требует авторизации через токен
    """
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Добавляем игру в список через модуль game_lists
    result = add_game(user_id, app_id, list_type, session)

    # Проверяем результат
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )

    return result

@router.delete("/users/me/game-lists/{app_id}", response_model=Dict[str, Any])
async def remove_game_from_user_list(
    app_id: int,
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Удаление игры из списка пользователя

    - **app_id**: ID игры

    Требует авторизации через токен
    """
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Удаляем игру из списка через модуль game_lists
    result = remove_game_from_list(user_id, app_id, session)

    # Проверяем результат
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["error"]
        )

    return result

@router.get("/users/me/game-lists", response_model=Dict[str, List])
async def get_all_user_game_lists(
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Получение всех списков игр пользователя

    Требует авторизации через токен
    """
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Получаем все списки пользователя через модуль game_lists
    result = get_user_game_lists(user_id, session)

    # Проверяем результат
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return result


@router.post("/users/me/avatar", response_model=Dict[str, Any])
async def upload_avatar(
    avatar: UploadFile = File(...),
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Загрузка аватара для текущего пользователя

    - Поддерживаемые форматы: JPEG, PNG, GIF
    - Максимальный размер: 2 МБ

    Требует авторизации через токен
    """
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Проверяем тип файла
    content_type = avatar.content_type
    if content_type not in ["image/jpeg", "image/png", "image/gif"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Поддерживаются только форматы JPEG, PNG и GIF"
        )

    # Читаем содержимое файла
    contents = await avatar.read()

    # Проверяем размер файла (максимум 2 МБ)
    if len(contents) > 2 * 1024 * 1024:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Размер файла не должен превышать 2 МБ"
        )

    # Получаем пользователя из базы данных
    user = session.exec(select(UserModel).where(UserModel.id == user_id)).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Обновляем аватар пользователя
    user.avatar = contents
    user.avatar_content_type = content_type

    # Сохраняем изменения в базе данных
    session.add(user)
    session.commit()

    return {
        "success": True,
        "message": "Аватар успешно загружен",
        "content_type": content_type
    }


@router.get("/users/{user_id}/avatar")
async def get_user_avatar(
    user_id: int,
    session: Session = Depends(get_session)
):
    """
    Получение аватара пользователя по его ID

    Возвращает изображение аватара в исходном формате
    """
    # Получаем пользователя из базы данных
    user = session.exec(select(UserModel).where(UserModel.id == user_id)).first()

    if not user or not user.avatar:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Аватар не найден"
        )

    # Возвращаем изображение
    return Response(
        content=user.avatar,
        media_type=user.avatar_content_type
    )


@router.delete("/users/me/avatar", response_model=Dict[str, Any])
async def delete_avatar(
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Удаление аватара текущего пользователя

    Требует авторизации через токен
    """
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Получаем пользователя из базы данных
    user = session.exec(select(UserModel).where(UserModel.id == user_id)).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Удаляем аватар
    user.avatar = None
    user.avatar_content_type = None

    # Сохраняем изменения в базе данных
    session.add(user)
    session.commit()

    return {
        "success": True,
        "message": "Аватар успешно удален"
    }