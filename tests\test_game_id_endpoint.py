"""
Тестирование нового эндпоинта для получения ID игры по названию
"""
import os
import sys
import asyncio
import httpx

# Добавляем текущую директорию в путь Python
sys.path.append(os.path.abspath('.'))

from sqlmodel import Session, select
from src.database import create_db_and_tables, engine
from src.models.models import Game

async def test_game_id_endpoint():
    """Тестирование эндпоинта /games/id-by-title/{title}"""
    print("Тестирование эндпоинта для получения ID игры по названию...")
    
    # Сначала получим несколько игр из базы данных для тестирования
    with Session(engine) as session:
        games = session.exec(select(Game).limit(3)).all()
        
        if not games:
            print("❌ В базе данных нет игр для тестирования")
            return
        
        print(f"✓ Найдено {len(games)} игр для тестирования:")
        for i, game in enumerate(games, 1):
            print(f"  {i}. {game.title} (ID: {game.app_id})")
    
    # Базовый URL для тестирования (предполагаем, что сервер запущен на localhost:8000)
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient() as client:
        # Тест 1: Поиск по точному названию
        print(f"\n1. Тестирование поиска по точному названию: '{games[0].title}'")
        try:
            response = await client.get(f"{base_url}/games/id-by-title/{games[0].title}")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Успешно найдена игра:")
                print(f"  - Запрос: {data.get('query')}")
                print(f"  - Найдено: {data.get('found')}")
                print(f"  - Точное совпадение: {data.get('exact_match')}")
                print(f"  - ID игры: {data.get('game_id')}")
                print(f"  - Название: {data.get('game_title')}")
            else:
                print(f"❌ Ошибка: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ Ошибка при запросе: {str(e)}")
        
        # Тест 2: Поиск по части названия
        print(f"\n2. Тестирование поиска по части названия")
        test_title = games[0].title[:5]  # Берем первые 5 символов
        print(f"Ищем игры содержащие: '{test_title}'")
        try:
            response = await client.get(f"{base_url}/games/id-by-title/{test_title}")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Результат поиска:")
                print(f"  - Запрос: {data.get('query')}")
                print(f"  - Найдено: {data.get('found')}")
                print(f"  - Точное совпадение: {data.get('exact_match')}")
                
                if data.get('multiple_results'):
                    print(f"  - Найдено игр: {data.get('count')}")
                    print("  - Список игр:")
                    for game in data.get('games', [])[:3]:  # Показываем только первые 3
                        print(f"    • {game['title']} (ID: {game['app_id']})")
                else:
                    print(f"  - ID игры: {data.get('game_id')}")
                    print(f"  - Название: {data.get('game_title')}")
            else:
                print(f"❌ Ошибка: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ Ошибка при запросе: {str(e)}")
        
        # Тест 3: Поиск несуществующей игры
        print(f"\n3. Тестирование поиска несуществующей игры")
        fake_title = "НесуществующаяИгра12345"
        print(f"Ищем игру: '{fake_title}'")
        try:
            response = await client.get(f"{base_url}/games/id-by-title/{fake_title}")
            if response.status_code == 404:
                print("✓ Корректно возвращена ошибка 404 для несуществующей игры")
                print(f"  - Сообщение: {response.json().get('detail')}")
            else:
                print(f"❌ Неожиданный статус код: {response.status_code}")
                print(f"  - Ответ: {response.text}")
        except Exception as e:
            print(f"❌ Ошибка при запросе: {str(e)}")
        
        # Тест 4: Поиск популярной игры (если есть)
        print(f"\n4. Тестирование поиска популярных игр")
        popular_searches = ["Counter", "Dota", "Steam", "Half", "Portal"]
        
        for search_term in popular_searches:
            print(f"Ищем игры содержащие: '{search_term}'")
            try:
                response = await client.get(f"{base_url}/games/id-by-title/{search_term}")
                if response.status_code == 200:
                    data = response.json()
                    if data.get('found'):
                        if data.get('multiple_results'):
                            print(f"  ✓ Найдено {data.get('count')} игр")
                        else:
                            print(f"  ✓ Найдена игра: {data.get('game_title')} (ID: {data.get('game_id')})")
                        break  # Прекращаем поиск после первого успешного результата
                    else:
                        print(f"  - Игры не найдены")
                elif response.status_code == 404:
                    print(f"  - Игры не найдены (404)")
                else:
                    print(f"  ❌ Ошибка: {response.status_code}")
            except Exception as e:
                print(f"  ❌ Ошибка при запросе: {str(e)}")

def main():
    """Основная функция тестирования"""
    print("Начинаем тестирование нового эндпоинта...")
    print("ВАЖНО: Убедитесь, что сервер запущен на localhost:8000")
    print("Для запуска сервера используйте: uvicorn main:app --reload")
    
    # Создаем таблицы (если нужно)
    create_db_and_tables()
    
    # Запускаем асинхронные тесты
    asyncio.run(test_game_id_endpoint())
    
    print("\nТестирование завершено!")

if __name__ == "__main__":
    main()
