"""
Демонстрация работы функционала поиска ID игры по названию
Этот скрипт тестирует логику без запуска веб-сервера
"""
import os
import sys

# Добавляем текущую директорию в путь Python
sys.path.append(os.path.abspath('.'))

from sqlmodel import Session, select
from src.database import create_db_and_tables, engine
from src.models.models import Game
from src.game_recommender_db import GameRecommender

def test_game_search_logic():
    """Тестирование логики поиска игр"""
    print("Демонстрация работы поиска ID игры по названию...")
    
    # Создаем экземпляр рекомендательной системы
    print("\n1. Инициализация рекомендательной системы...")
    recommender = GameRecommender()
    print("✓ Рекомендательная система инициализирована")
    
    # Получаем несколько игр из базы данных для демонстрации
    print("\n2. Получение игр из базы данных...")
    with Session(engine) as session:
        games = session.exec(select(Game).limit(5)).all()
        
        if not games:
            print("❌ В базе данных нет игр для демонстрации")
            return
        
        print(f"✓ Найдено {len(games)} игр:")
        for i, game in enumerate(games, 1):
            print(f"  {i}. {game.title} (ID: {game.app_id})")
    
    # Демонстрация различных сценариев поиска
    test_cases = [
        {
            "title": games[0].title,
            "description": "Поиск по точному названию"
        },
        {
            "title": games[0].title[:10],
            "description": "Поиск по части названия"
        },
        {
            "title": "Counter",
            "description": "Поиск популярной игры"
        },
        {
            "title": "НесуществующаяИгра",
            "description": "Поиск несуществующей игры"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 3):
        print(f"\n{i}. {test_case['description']}: '{test_case['title']}'")
        
        # Сначала пробуем точный поиск
        result = recommender.recommend_by_title(test_case['title'], top_n=1)
        
        if "error" not in result and "game" in result:
            print("✓ Найдено точное совпадение:")
            print(f"  - ID игры: {result['game']['app_id']}")
            print(f"  - Название: {result['game']['title']}")
            print(f"  - Рейтинг: {result['game'].get('rating', 'Не указан')}")
        else:
            # Если точного совпадения нет, используем поиск по части названия
            search_results = recommender.search_games_by_title(test_case['title'])
            
            if not search_results:
                print("❌ Игры не найдены")
            elif len(search_results) == 1:
                game = search_results[0]
                print("✓ Найдена одна игра:")
                print(f"  - ID игры: {game['app_id']}")
                print(f"  - Название: {game['title']}")
                print(f"  - Рейтинг: {game.get('rating', 'Не указан')}")
            else:
                print(f"✓ Найдено {len(search_results)} игр:")
                for j, game in enumerate(search_results[:5], 1):  # Показываем только первые 5
                    print(f"  {j}. {game['title']} (ID: {game['app_id']})")
                if len(search_results) > 5:
                    print(f"  ... и еще {len(search_results) - 5} игр")

def simulate_endpoint_response(title: str):
    """Симуляция ответа нашего эндпоинта"""
    print(f"\n--- Симуляция эндпоинта для '{title}' ---")
    
    recommender = GameRecommender()
    
    try:
        # Логика из нашего эндпоинта
        result = recommender.recommend_by_title(title, top_n=1)
        
        if "error" not in result and "game" in result:
            response = {
                "query": title,
                "found": True,
                "exact_match": True,
                "game_id": result["game"]["app_id"],
                "game_title": result["game"]["title"]
            }
            print("✓ Ответ эндпоинта (точное совпадение):")
        else:
            search_results = recommender.search_games_by_title(title)
            
            if not search_results:
                print("❌ Ответ эндпоинта: 404 - Игра не найдена")
                return
            
            if len(search_results) == 1:
                game = search_results[0]
                response = {
                    "query": title,
                    "found": True,
                    "exact_match": False,
                    "game_id": game["app_id"],
                    "game_title": game["title"]
                }
                print("✓ Ответ эндпоинта (одна игра найдена):")
            else:
                games_list = []
                for game in search_results[:10]:
                    games_list.append({
                        "app_id": game["app_id"],
                        "title": game["title"]
                    })
                
                response = {
                    "query": title,
                    "found": True,
                    "exact_match": False,
                    "multiple_results": True,
                    "count": len(games_list),
                    "games": games_list
                }
                print("✓ Ответ эндпоинта (несколько игр найдено):")
        
        # Выводим JSON-подобный ответ
        import json
        print(json.dumps(response, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"❌ Ошибка: {str(e)}")

def main():
    """Основная функция демонстрации"""
    print("Демонстрация функционала поиска ID игры по названию")
    print("=" * 60)
    
    # Создаем таблицы (если нужно)
    create_db_and_tables()
    
    # Тестируем логику поиска
    test_game_search_logic()
    
    print("\n" + "=" * 60)
    print("Симуляция ответов эндпоинта:")
    
    # Симулируем несколько запросов к эндпоинту
    test_queries = ["Counter", "Dota", "Half", "Portal", "Steam"]
    
    for query in test_queries:
        simulate_endpoint_response(query)
    
    print("\n" + "=" * 60)
    print("Демонстрация завершена!")
    print("\nДля тестирования реального эндпоинта:")
    print("1. Запустите сервер: uvicorn main:app --reload")
    print("2. Запустите тест: python tests/test_game_id_endpoint.py")
    print("3. Или откройте в браузере: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
