from fastapi import APIRouter, HTTPException, Query, Request
from fastapi.responses import JSONResponse, HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from typing import List
from src.game_recommender_db import GameRecommender
import os
import httpx
from src.api.steamapi import get_steam_game_image

router = APIRouter()

# Создаем глобальный экземпляр рекомендательной системы
recommender = GameRecommender()

# Путь к статическим файлам
static_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "static")

@router.get("/", tags=["Главная"])
async def root():
    """Перенаправляет на главную страницу приложения"""
    return RedirectResponse(url="/static/index.html")


@router.get("/recommend/{title}", tags=["Рекомендации"])
async def recommend_games_by_title(
    title: str,
    top_n: int = Query(10, ge=1, le=20)
):
    """
    Получение рекомендаций для игры по её названию

    - **title**: Название игры
    - **top_n**: Количество рекомендаций (от 1 до 20)
    """
    result = recommender.recommend_by_title(title, top_n)
    if "error" in result:
        raise HTTPException(status_code=404, detail=result["error"])
    return JSONResponse(content=result)


@router.get("/games", tags=["Игры"])
async def get_all_games(limit: int = Query(100, ge=1, le=1000)):
    """Получение списка всех игр с ограничением по количеству"""
    return JSONResponse(content=recommender.get_all_games(limit=limit))


@router.get("/games/filter/tags", tags=["Параметры фильтрации"])
async def get_available_tags(limit: int = Query(100, ge=1, le=500, description="Максимальное количество тегов для возврата")):
    """Получение списка доступных тегов для фильтрации игр"""
    return JSONResponse(content=recommender.get_available_tags(limit=limit))


@router.get("/games/filter/years", tags=["Параметры фильтрации"])
async def get_available_years():
    """Получение списка доступных годов выпуска для фильтрации игр"""
    return JSONResponse(content=recommender.get_available_years())


@router.get("/games/filter/ratings", tags=["Параметры фильтрации"])
async def get_available_ratings():
    """Получение списка доступных рейтингов для фильтрации игр"""
    return JSONResponse(content=recommender.get_available_ratings())


@router.get("/games/filter/os", tags=["Параметры фильтрации"])
async def get_available_os():
    """Получение списка доступных операционных систем для фильтрации игр"""
    return JSONResponse(content=[
        {"os": "win", "name": "Windows"},
        {"os": "mac", "name": "macOS"},
        {"os": "linux", "name": "Linux"}
    ])


@router.get("/games/filter", tags=["Игры"])
async def filter_games(
    year_min: int = Query(None, description="Минимальный год выпуска игры. Оставьте пустым, чтобы не фильтровать по минимальному году."),
    year_max: int = Query(None, description="Максимальный год выпуска игры. Оставьте пустым, чтобы не фильтровать по максимальному году."),
    tags: List[str] = Query(None, description="Список тегов игры (например, Action, RPG, Strategy). Можно указать несколько тегов. Оставьте пустым, чтобы не фильтровать по тегам."),
    os: str = Query(None, description="Операционная система: win, mac, linux. Оставьте пустым, чтобы не фильтровать по ОС."),
    rating: str = Query(None, description="Рейтинг игры (например, Very Positive, Positive, Mixed). Оставьте пустым, чтобы не фильтровать по рейтингу."),
    min_positive_ratio: int = Query(None, ge=0, le=100, description="Минимальный процент положительных отзывов (0-100). Оставьте пустым, чтобы не фильтровать по этому параметру."),
    steam_deck: bool = Query(None, description="Поддержка Steam Deck (True/False). Оставьте пустым, чтобы не фильтровать по этому параметру.")
):
    return JSONResponse(content=recommender.filter_games(
        year_min=year_min,
        year_max=year_max,
        tags=tags,
        os=os,
        rating=rating,
        min_positive_ratio=min_positive_ratio,
        steam_deck=steam_deck
    ))


@router.get("/games/search/{title}", tags=["Игры"])
async def search_games(title: str):
    """Поиск игр по названию или части названия"""
    return JSONResponse(content=recommender.search_games_by_title(title))


@router.get("/games/id-by-title/{title}", tags=["Игры"])
async def get_game_id_by_title(title: str):
    """
    Получение ID игры по её точному названию

    - **title**: Точное полное название игры

    Возвращает ID игры только при точном совпадении названия.
    Если игра с таким точным названием не найдена, возвращает ошибку 404.
    """
    try:
        # Ищем игру с точным названием в базе данных
        games_df = recommender.games
        exact_match = games_df[games_df['title'] == title]

        if exact_match.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Игра с точным названием '{title}' не найдена"
            )

        # Берем первую найденную игру (должна быть только одна с точным названием)
        game = exact_match.iloc[0]

        return JSONResponse(content={
            "query": title,
            "found": True,
            "game_id": int(game["app_id"]),
            "game_title": game["title"]
        })

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка при поиске игры: {str(e)}"
        )


@router.get("/games/autocomplete", tags=["Игры"])
async def autocomplete_game_title(
    query: str = Query(..., description="Строка запроса для автокомплита"),
    limit: int = Query(10, ge=1, le=20, description="Максимальное количество результатов")
):
    """
    Автодополнение названий игр по введенному запросу

    - **query**: Строка запроса для автодополнения
    - **limit**: Максимальное количество результатов (от 1 до 20)

    Возвращает список названий игр, подходящих для автодополнения.
    Если запрос пустой или слишком короткий (менее 2 символов), возвращает популярные игры.
    """
    return JSONResponse(content=recommender.autocomplete_game_title(query, limit))


@router.get("/games/{app_id}", tags=["Games"])
async def get_game_by_id(app_id: int):
    """Получение информации об игре по ID, включая изображение из Steam и все метаданные"""
    game = recommender.get_game_by_id(app_id)
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")

    # Получаем изображение игры из Steam API
    try:
        # Используем тот же подход, что и в эндпоинте get_game_image
        image_url = await get_steam_game_image(app_id)
        game['image_url'] = image_url
    except Exception as e:
        # Логируем ошибку для отладки
        print(f"Ошибка при получении изображения для игры {app_id}: {str(e)}")
        # Устанавливаем пустую строку вместо None, чтобы поведение было более предсказуемым
        game['image_url'] = ""

    # Убедимся, что все метаданные включены в ответ
    # Метаданные уже должны быть добавлены в get_game_by_id, но проверим это
    if 'description' not in game:
        game['description'] = ''
    if 'tags' not in game:
        game['tags'] = []

    return JSONResponse(content=game)


@router.get("/recommend/user/{user_id}", tags=["Recommendations"])
async def recommend_games_for_user(
    user_id: int,
    top_n: int = Query(10, ge=1, le=20, description="Количество рекомендаций (от 1 до 20)")
):
    """
    Получение рекомендаций игр для конкретного пользователя на основе его оценок

    Использует гибридный подход для построения рекомендаций:
    1. Если у пользователя есть положительные оценки, использует коллаборативную фильтрацию
       на основе оценок пользователя из сбалансированной таблицы.
    2. Если у пользователя нет положительных оценок, рекомендует популярные игры,
       исключая те, которые пользователь уже оценил.

    - **user_id**: ID пользователя
    - **top_n**: Количество рекомендаций (от 1 до 20)
    """
    result = recommender.recommend_for_user(user_id, top_n)
    if "error" in result:
        raise HTTPException(status_code=404, detail=result["error"])
    return JSONResponse(content=result)


@router.get("/recommend/user/{user_id}/item-based", tags=["Recommendations"])
async def recommend_games_for_user_item_based(
    user_id: int,
    top_n: int = Query(10, ge=1, le=20, description="Количество рекомендаций (от 1 до 20)")
):
    """
    Получение рекомендаций игр для конкретного пользователя на основе item-based коллаборативной фильтрации

    Использует item-based коллаборативную фильтрацию для построения рекомендаций:
    1. Для каждой положительно оцененной пользователем игры находит похожие игры
       на основе оценок других пользователей.
    2. Если у пользователя нет положительных оценок, рекомендует популярные игры,
       исключая те, которые пользователь уже оценил.

    В отличие от обычных рекомендаций, этот метод показывает, на основе какой игры
    была сделана каждая рекомендация.

    - **user_id**: ID пользователя
    - **top_n**: Количество рекомендаций (от 1 до 20)
    """
    result = recommender.recommend_for_user_item_based(user_id, top_n)
    if "error" in result:
        raise HTTPException(status_code=404, detail=result["error"])
    return JSONResponse(content=result)


@router.get("/get_game_image/{app_id}", tags=["Игры"])
async def get_game_image(app_id: int):
    """Получение URL изображения игры из Steam API"""
    try:
        image_url = await get_steam_game_image(app_id)
        return {"image_url": image_url, "app_id": app_id}
    except Exception as e:
        # Логируем ошибку для отладки
        print(f"Ошибка при получении изображения для игры {app_id}: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))